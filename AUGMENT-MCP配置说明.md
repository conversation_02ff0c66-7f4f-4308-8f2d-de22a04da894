# AUGMENT 全局MCP配置说明文档

## 📍 配置文件位置

### 主配置目录
```
C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment\
```

### 关键配置文件
- **主配置文件**: `augment-global-state\mcpServers.json` (120KB)
- **终端配置**: `augment-global-state\terminalSettings.json` (233字节)
- **工具配置**: `augment-user-assets\tool-configs\`

## 🔧 已配置的MCP服务器

### 1. 寸止 (智能交互工具)
- **类型**: stdio
- **命令**: `寸止`
- **工具数量**: 2个
- **主要功能**: 
  - `zhi___`: 智能代码审查交互工具
  - `ji___`: 全局记忆管理工具

### 2. TradeFusion Database (数据库服务器)
- **类型**: stdio
- **命令**: `node F:\MCP\executeautomation-database-server\node_modules\@executeautomation\database-server\dist\src\index.js`
- **数据库**: `E:\TradeFusion\数据库0_实体模块\股票数据.db`
- **工具数量**: 10个
- **主要功能**: 
  - 数据库查询 (SELECT, INSERT, UPDATE, DELETE)
  - 表管理 (创建、修改、删除表)
  - 数据导出 (CSV, JSON)
  - 业务洞察管理

### 3. GitHub MCP Server (GitHub集成)
- **类型**: stdio
- **命令**: `F:\MCP\github-mcp-server\github-mcp-server.exe stdio`
- **工具数量**: 87个
- **主要功能**:
  - 仓库管理 (创建、分支、标签)
  - Issue管理 (创建、评论、搜索)
  - Pull Request操作 (创建、审查、合并)
  - 工作流管理 (运行、监控、日志)
  - 通知管理
  - 代码扫描和安全警报

### 4. FileScopeMCP (文件管理)
- **类型**: stdio
- **命令**: `npx -y @modelcontextprotocol/server-filesystem`
- **工具数量**: 19个
- **主要功能**:
  - 文件树管理和分析
  - 文件重要性评估
  - 项目结构图表生成
  - 文件监控和变更检测

### 5. Context 7 (文档查询)
- **类型**: stdio
- **命令**: `npx -y @upstash/context7-mcp@latest`
- **工具数量**: 2个
- **主要功能**:
  - 库文档ID解析
  - 最新技术文档获取

### 6. Playwright (浏览器自动化)
- **类型**: stdio
- **命令**: `npx -y @playwright/mcp@latest`
- **工具数量**: 25个
- **主要功能**:
  - 浏览器控制 (导航、点击、输入)
  - 页面截图和快照
  - 网络请求监控
  - 多标签页管理

### 7. Sequential thinking (思维链工具)
- **类型**: stdio
- **命令**: `npx -y @modelcontextprotocol/server-sequential-thinking`
- **工具数量**: 1个
- **主要功能**:
  - 复杂问题分步思考
  - 动态思维过程调整

### 8. ssh-mcp (SSH远程连接)
- **类型**: stdio
- **命令**: `node F:\MCP\ssh-mcp-server\build\index.js`
- **连接参数**:
  - 主机: `*************:22`
  - 用户: `ubuntu`
  - 密码: `ymjatTUU520`
  - 超时: 30秒
- **工具数量**: 1个
- **主要功能**: 远程Shell命令执行

### 9. tencent-tat (腾讯云TAT)
- **类型**: stdio
- **命令**: `F:/MCP/tat-mcp-server/env/Scripts/mcp-server-tat.exe`
- **环境变量**:
  - `TENCENTCLOUD_SECRET_ID`: AKIDriTPHzVu8IFzWr0weATZS8cthP5cw3yh
  - `TENCENTCLOUD_SECRET_KEY`: f9TdeWI1e8Rm5ECyXt2yhCVbacUAlLm2
  - `TENCENTCLOUD_REGION`: ap-shanghai
  - `DEFAULT_INSTANCE_ID`: lhins-m9amye7a
- **主要功能**: 腾讯云服务器命令执行

## 📊 配置统计

| 项目 | 数量/信息 |
|------|-----------|
| MCP服务器总数 | 9个 |
| 工具总数 | 141个 |
| 配置文件大小 | 120KB |
| 最后更新时间 | 2025-07-28 14:51 |

## 🔒 安全配置

- 所有服务器状态: **已启用** (`enabled: true`)
- 所有工具状态: **已配置** (`isConfigured: true`)
- 工具安全级别: **0级** (最高权限)
- Shell插值: **已启用** (`useShellInterpolation: true`)

## 📁 目录结构

```
C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment\
├── augment-global-state\
│   ├── mcpServers.json          # 主要MCP服务器配置 (120KB)
│   └── terminalSettings.json    # 终端设置配置 (233B)
└── augment-user-assets\
    └── tool-configs\
        └── approval\
            └── manual\
```

## 🛠️ 维护说明

### 备份配置
建议定期备份配置文件：
```bash
Copy-Item "C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment\augment-global-state\mcpServers.json" "备份路径"
```

### 修改配置
1. 关闭AUGMENT
2. 编辑配置文件
3. 验证JSON格式
4. 重启AUGMENT

### 故障排除
- 检查服务器可执行文件路径
- 验证环境变量设置
- 确认网络连接状态
- 查看AUGMENT日志

## 📝 注意事项

⚠️ **重要提醒**:
- 配置文件包含敏感信息 (SSH密码、云服务密钥)
- 修改前请备份原始配置
- 某些服务器依赖外部网络连接
- 工具权限级别较高，使用时需谨慎

---
*文档生成时间: 2025-07-28*  
*配置文件版本: mcpServers.json (120,384 bytes)*
