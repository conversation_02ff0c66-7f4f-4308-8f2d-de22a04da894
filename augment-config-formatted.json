[{"type": "stdio", "name": "寸止", "command": "寸止", "arguments": "", "useShellInterpolation": true, "id": "69bbc321-830e-484c-97bc-743c5b2e1679", "tools": [{"definition": {"name": "zhi___", "description": "智能代码审查交互工具，支持预定义选项、自由文本输入和图片上传", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"is_markdown\":{\"description\":\"消息是否为Markdown格式，默认为true\",\"type\":\"boolean\"},\"message\":{\"description\":\"要显示给用户的消息\",\"type\":\"string\"},\"predefined_options\":{\"description\":\"预定义的选项列表（可选）\",\"items\":{\"type\":\"string\"},\"type\":\"array\"}},\"required\":[\"message\"]}", "tool_safety": 0, "original_mcp_server_name": "寸止", "mcp_server_name": "__", "mcp_tool_name": "zhi"}, "identifier": {"hostName": "mcpHost", "toolId": "zhi___"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "ji___", "description": "全局记忆管理工具，用于存储和管理重要的开发规范、用户偏好和最佳实践", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"action\":{\"description\":\"操作类型：记忆(添加记忆), 回忆(获取项目信息)\",\"type\":\"string\"},\"category\":{\"description\":\"记忆分类：rule(规范规则), preference(用户偏好), pattern(最佳实践), context(项目上下文)\",\"type\":\"string\"},\"content\":{\"description\":\"记忆内容（记忆操作时必需）\",\"type\":\"string\"},\"project_path\":{\"description\":\"项目路径（必需）\",\"type\":\"string\"}},\"required\":[\"action\",\"project_path\"]}", "tool_safety": 0, "original_mcp_server_name": "寸止", "mcp_server_name": "__", "mcp_tool_name": "ji"}, "identifier": {"hostName": "mcpHost", "toolId": "ji___"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabled": false}, {"type": "stdio", "name": "TradeFusion Database", "command": "node F:\\MCP\\executeautomation-database-server\\node_modules\\@executeautomation\\database-server\\dist\\src\\index.js E:\\TradeFusion\\数据库0_实体模块\\股票数据.db", "arguments": "", "useShellInterpolation": true, "id": "49110ea4-4d82-4f21-b692-8f64a63ebc10", "tools": [{"definition": {"name": "read_query_TradeFusion_Database", "description": "Execute SELECT queries to read data from the database", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "read_query"}, "identifier": {"hostName": "mcpHost", "toolId": "read_query_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "write_query_TradeFusion_Database", "description": "Execute INSERT, UPDATE, or DELETE queries", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "write_query"}, "identifier": {"hostName": "mcpHost", "toolId": "write_query_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_table_TradeFusion_Database", "description": "Create new tables in the database", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "create_table"}, "identifier": {"hostName": "mcpHost", "toolId": "create_table_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "alter_table_TradeFusion_Database", "description": "Modify existing table schema (add columns, rename tables, etc.)", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "alter_table"}, "identifier": {"hostName": "mcpHost", "toolId": "alter_table_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "drop_table_TradeFusion_Database", "description": "Remove a table from the database with safety confirmation", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"table_name\":{\"type\":\"string\"},\"confirm\":{\"type\":\"boolean\"}},\"required\":[\"table_name\",\"confirm\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "drop_table"}, "identifier": {"hostName": "mcpHost", "toolId": "drop_table_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "export_query_TradeFusion_Database", "description": "Export query results to various formats (CSV, JSON)", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\"},\"format\":{\"type\":\"string\",\"enum\":[\"csv\",\"json\"]}},\"required\":[\"query\",\"format\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "export_query"}, "identifier": {"hostName": "mcpHost", "toolId": "export_query_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_tables_TradeFusion_Database", "description": "Get a list of all tables in the database", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "list_tables"}, "identifier": {"hostName": "mcpHost", "toolId": "list_tables_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "describe_table_TradeFusion_Database", "description": "View schema information for a specific table", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"table_name\":{\"type\":\"string\"}},\"required\":[\"table_name\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "describe_table"}, "identifier": {"hostName": "mcpHost", "toolId": "describe_table_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "append_insight_TradeFusion_Database", "description": "Add a business insight to the memo", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"insight\":{\"type\":\"string\"}},\"required\":[\"insight\"]}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "append_insight"}, "identifier": {"hostName": "mcpHost", "toolId": "append_insight_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_insights_TradeFusion_Database", "description": "List all business insights in the memo", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "TradeFusion Database", "mcp_server_name": "TradeFusion_Database", "mcp_tool_name": "list_insights"}, "identifier": {"hostName": "mcpHost", "toolId": "list_insights_TradeFusion_Database"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabled": false}, {"type": "stdio", "name": "GitHub MCP Server", "command": "F:\\MCP\\github-mcp-server\\github-mcp-server.exe stdio", "arguments": "", "useShellInterpolation": true, "id": "9efa3f14-b4e0-4ac5-a2a2-211a7b06fa02", "tools": [{"definition": {"name": "add_issue_comment_GitHub_MCP_Server", "description": "Add a comment to a specific issue in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"body\":{\"description\":\"Comment content\",\"type\":\"string\"},\"issue_number\":{\"description\":\"Issue number to comment on\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"issue_number\",\"body\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "add_issue_comment"}, "identifier": {"hostName": "mcpHost", "toolId": "add_issue_comment_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "add_pull_request_review_comment_to_pending_review_GitHub_MCP_Ser", "description": "Add a comment to the requester's latest pending pull request review, a pending review needs to already exist to call this (check with the user if not sure).", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"body\":{\"description\":\"The text of the review comment\",\"type\":\"string\"},\"line\":{\"description\":\"The line of the blob in the pull request diff that the comment applies to. For multi-line comments, the last line of the range\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"path\":{\"description\":\"The relative path to the file that necessitates a comment\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"side\":{\"description\":\"The side of the diff to comment on. LEFT indicates the previous state, RIGHT indicates the new state\",\"enum\":[\"LEFT\",\"RIGHT\"],\"type\":\"string\"},\"startLine\":{\"description\":\"For multi-line comments, the first line of the range that the comment applies to\",\"type\":\"number\"},\"startSide\":{\"description\":\"For multi-line comments, the starting side of the diff that the comment applies to. LEFT indicates the previous state, RIGHT indicates the new state\",\"enum\":[\"LEFT\",\"RIGHT\"],\"type\":\"string\"},\"subjectType\":{\"description\":\"The level at which the comment is targeted\",\"enum\":[\"FILE\",\"LINE\"],\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\",\"path\",\"body\",\"subjectType\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "add_pull_request_review_comment_to_pending_review"}, "identifier": {"hostName": "mcpHost", "toolId": "add_pull_request_review_comment_to_pending_review_GitHub_MCP_Ser"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "assign_copilot_to_issue_GitHub_MCP_Server", "description": "Assign Copilot to a specific issue in a GitHub repository.\n\nThis tool can help with the following outcomes:\n- a Pull Request created with source code changes to resolve the issue\n\n\nMore information can be found at:\n- https://docs.github.com/en/copilot/using-github-copilot/using-copilot-coding-agent-to-work-on-tasks/about-assigning-tasks-to-copilot\n", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"issueNumber\":{\"description\":\"Issue number\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"issueNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "assign_copilot_to_issue"}, "identifier": {"hostName": "mcpHost", "toolId": "assign_copilot_to_issue_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "cancel_workflow_run_GitHub_MCP_Server", "description": "Cancel a workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "cancel_workflow_run"}, "identifier": {"hostName": "mcpHost", "toolId": "cancel_workflow_run_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_and_submit_pull_request_review_GitHub_MCP_Server", "description": "Create and submit a review for a pull request without review comments.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"body\":{\"description\":\"Review comment text\",\"type\":\"string\"},\"commitID\":{\"description\":\"SHA of commit to review\",\"type\":\"string\"},\"event\":{\"description\":\"Review action to perform\",\"enum\":[\"APPROVE\",\"REQUEST_CHANGES\",\"COMMENT\"],\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\",\"body\",\"event\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_and_submit_pull_request_review"}, "identifier": {"hostName": "mcpHost", "toolId": "create_and_submit_pull_request_review_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_branch_GitHub_MCP_Server", "description": "Create a new branch in a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"branch\":{\"description\":\"Name for new branch\",\"type\":\"string\"},\"from_branch\":{\"description\":\"Source branch (defaults to repo default)\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"branch\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_branch"}, "identifier": {"hostName": "mcpHost", "toolId": "create_branch_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_issue_GitHub_MCP_Server", "description": "Create a new issue in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"assignees\":{\"description\":\"Usernames to assign to this issue\",\"items\":{\"type\":\"string\"},\"type\":\"array\"},\"body\":{\"description\":\"Issue body content\",\"type\":\"string\"},\"labels\":{\"description\":\"Labels to apply to this issue\",\"items\":{\"type\":\"string\"},\"type\":\"array\"},\"milestone\":{\"description\":\"Milestone number\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"title\":{\"description\":\"Issue title\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"title\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_issue"}, "identifier": {"hostName": "mcpHost", "toolId": "create_issue_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_or_update_file_GitHub_MCP_Server", "description": "Create or update a single file in a GitHub repository. If updating, you must provide the SHA of the file you want to update. Use this tool to create or update a file in a GitHub repository remotely; do not use it for local file operations.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"branch\":{\"description\":\"Branch to create/update the file in\",\"type\":\"string\"},\"content\":{\"description\":\"Content of the file\",\"type\":\"string\"},\"message\":{\"description\":\"Commit message\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner (username or organization)\",\"type\":\"string\"},\"path\":{\"description\":\"Path where to create/update the file\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"sha\":{\"description\":\"Required if updating an existing file. The blob SHA of the file being replaced.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"path\",\"content\",\"message\",\"branch\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_or_update_file"}, "identifier": {"hostName": "mcpHost", "toolId": "create_or_update_file_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_pending_pull_request_review_GitHub_MCP_Server", "description": "Create a pending review for a pull request. Call this first before attempting to add comments to a pending review, and ultimately submitting it. A pending pull request review means a pull request review, it is pending because you create it first and submit it later, and the PR author will not see it until it is submitted.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"commitID\":{\"description\":\"SHA of commit to review\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_pending_pull_request_review"}, "identifier": {"hostName": "mcpHost", "toolId": "create_pending_pull_request_review_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_pull_request_GitHub_MCP_Server", "description": "Create a new pull request in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"base\":{\"description\":\"Branch to merge into\",\"type\":\"string\"},\"body\":{\"description\":\"PR description\",\"type\":\"string\"},\"draft\":{\"description\":\"Create as draft PR\",\"type\":\"boolean\"},\"head\":{\"description\":\"Branch containing changes\",\"type\":\"string\"},\"maintainer_can_modify\":{\"description\":\"Allow maintainer edits\",\"type\":\"boolean\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"title\":{\"description\":\"PR title\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"title\",\"head\",\"base\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_pull_request"}, "identifier": {"hostName": "mcpHost", "toolId": "create_pull_request_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_repository_GitHub_MCP_Server", "description": "Create a new GitHub repository in your account", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"autoInit\":{\"description\":\"Initialize with README\",\"type\":\"boolean\"},\"description\":{\"description\":\"Repository description\",\"type\":\"string\"},\"name\":{\"description\":\"Repository name\",\"type\":\"string\"},\"private\":{\"description\":\"Whether repo should be private\",\"type\":\"boolean\"}},\"required\":[\"name\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "create_repository"}, "identifier": {"hostName": "mcpHost", "toolId": "create_repository_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "delete_file_GitHub_MCP_Server", "description": "Delete a file from a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"branch\":{\"description\":\"Branch to delete the file from\",\"type\":\"string\"},\"message\":{\"description\":\"Commit message\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner (username or organization)\",\"type\":\"string\"},\"path\":{\"description\":\"Path to the file to delete\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"path\",\"message\",\"branch\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "delete_file"}, "identifier": {"hostName": "mcpHost", "toolId": "delete_file_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "delete_pending_pull_request_review_GitHub_MCP_Server", "description": "Delete the requester's latest pending pull request review. Use this after the user decides not to submit a pending review, if you don't know if they already created one then check first.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "delete_pending_pull_request_review"}, "identifier": {"hostName": "mcpHost", "toolId": "delete_pending_pull_request_review_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "delete_workflow_run_logs_GitHub_MCP_Server", "description": "Delete logs for a workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "delete_workflow_run_logs"}, "identifier": {"hostName": "mcpHost", "toolId": "delete_workflow_run_logs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "dismiss_notification_GitHub_MCP_Server", "description": "Dismiss a notification by marking it as read or done", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"state\":{\"description\":\"The new state of the notification (read/done)\",\"enum\":[\"read\",\"done\"],\"type\":\"string\"},\"threadID\":{\"description\":\"The ID of the notification thread\",\"type\":\"string\"}},\"required\":[\"threadID\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "dismiss_notification"}, "identifier": {"hostName": "mcpHost", "toolId": "dismiss_notification_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "download_workflow_run_artifact_GitHub_MCP_Server", "description": "Get download URL for a workflow run artifact", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"artifact_id\":{\"description\":\"The unique identifier of the artifact\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"artifact_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "download_workflow_run_artifact"}, "identifier": {"hostName": "mcpHost", "toolId": "download_workflow_run_artifact_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "fork_repository_GitHub_MCP_Server", "description": "Fork a GitHub repository to your account or specified organization", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"organization\":{\"description\":\"Organization to fork to\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "fork_repository"}, "identifier": {"hostName": "mcpHost", "toolId": "fork_repository_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_code_scanning_alert_GitHub_MCP_Server", "description": "Get details of a specific code scanning alert in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"alertNumber\":{\"description\":\"The number of the alert.\",\"type\":\"number\"},\"owner\":{\"description\":\"The owner of the repository.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"alertNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_code_scanning_alert"}, "identifier": {"hostName": "mcpHost", "toolId": "get_code_scanning_alert_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_commit_GitHub_MCP_Server", "description": "Get details for a commit from a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"sha\":{\"description\":\"Commit SHA, branch name, or tag name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"sha\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_commit"}, "identifier": {"hostName": "mcpHost", "toolId": "get_commit_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_dependabot_alert_GitHub_MCP_Server", "description": "Get details of a specific dependabot alert in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"alertNumber\":{\"description\":\"The number of the alert.\",\"type\":\"number\"},\"owner\":{\"description\":\"The owner of the repository.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"alertNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_dependabot_alert"}, "identifier": {"hostName": "mcpHost", "toolId": "get_dependabot_alert_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_discussion_GitHub_MCP_Server", "description": "Get a specific discussion by ID", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"discussionNumber\":{\"description\":\"Discussion Number\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"discussionNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_discussion"}, "identifier": {"hostName": "mcpHost", "toolId": "get_discussion_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_discussion_comments_GitHub_MCP_Server", "description": "Get comments from a discussion", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"discussionNumber\":{\"description\":\"Discussion Number\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"discussionNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_discussion_comments"}, "identifier": {"hostName": "mcpHost", "toolId": "get_discussion_comments_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_file_contents_GitHub_MCP_Server", "description": "Get the contents of a file or directory from a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner (username or organization)\",\"type\":\"string\"},\"path\":{\"default\":\"/\",\"description\":\"Path to file/directory (directories must end with a slash '/')\",\"type\":\"string\"},\"ref\":{\"description\":\"Accepts optional git refs such as `refs/tags/{tag}`, `refs/heads/{branch}` or `refs/pull/{pr_number}/head`\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"sha\":{\"description\":\"Accepts optional commit SHA. If specified, it will be used instead of ref\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_file_contents"}, "identifier": {"hostName": "mcpHost", "toolId": "get_file_contents_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_issue_GitHub_MCP_Server", "description": "Get details of a specific issue in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"issue_number\":{\"description\":\"The number of the issue\",\"type\":\"number\"},\"owner\":{\"description\":\"The owner of the repository\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"issue_number\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_issue"}, "identifier": {"hostName": "mcpHost", "toolId": "get_issue_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_issue_comments_GitHub_MCP_Server", "description": "Get comments for a specific issue in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"issue_number\":{\"description\":\"Issue number\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"issue_number\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_issue_comments"}, "identifier": {"hostName": "mcpHost", "toolId": "get_issue_comments_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_job_logs_GitHub_MCP_Server", "description": "Download logs for a specific workflow job or efficiently get all failed job logs for a workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"failed_only\":{\"description\":\"When true, gets logs for all failed jobs in run_id\",\"type\":\"boolean\"},\"job_id\":{\"description\":\"The unique identifier of the workflow job (required for single job logs)\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"return_content\":{\"description\":\"Returns actual log content instead of URLs\",\"type\":\"boolean\"},\"run_id\":{\"description\":\"Workflow run ID (required when using failed_only)\",\"type\":\"number\"},\"tail_lines\":{\"default\":500,\"description\":\"Number of lines to return from the end of the log\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_job_logs"}, "identifier": {"hostName": "mcpHost", "toolId": "get_job_logs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_me_GitHub_MCP_Server", "description": "Get details of the authenticated GitHub user. Use this when a request is about the user's own profile for GitHub. Or when information is missing to build other tool calls.", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_me"}, "identifier": {"hostName": "mcpHost", "toolId": "get_me_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_notification_details_GitHub_MCP_Server", "description": "Get detailed information for a specific GitHub notification, always call this tool when the user asks for details about a specific notification, if you don't know the ID list notifications first.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"notificationID\":{\"description\":\"The ID of the notification\",\"type\":\"string\"}},\"required\":[\"notificationID\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_notification_details"}, "identifier": {"hostName": "mcpHost", "toolId": "get_notification_details_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_pull_request_GitHub_MCP_Server", "description": "Get details of a specific pull request in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_pull_request"}, "identifier": {"hostName": "mcpHost", "toolId": "get_pull_request_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_pull_request_comments_GitHub_MCP_Server", "description": "Get comments for a specific pull request.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_pull_request_comments"}, "identifier": {"hostName": "mcpHost", "toolId": "get_pull_request_comments_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_pull_request_diff_GitHub_MCP_Server", "description": "Get the diff of a pull request.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_pull_request_diff"}, "identifier": {"hostName": "mcpHost", "toolId": "get_pull_request_diff_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_pull_request_files_GitHub_MCP_Server", "description": "Get the files changed in a specific pull request.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_pull_request_files"}, "identifier": {"hostName": "mcpHost", "toolId": "get_pull_request_files_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_pull_request_reviews_GitHub_MCP_Server", "description": "Get reviews for a specific pull request.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_pull_request_reviews"}, "identifier": {"hostName": "mcpHost", "toolId": "get_pull_request_reviews_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_pull_request_status_GitHub_MCP_Server", "description": "Get the status of a specific pull request.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_pull_request_status"}, "identifier": {"hostName": "mcpHost", "toolId": "get_pull_request_status_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_secret_scanning_alert_GitHub_MCP_Server", "description": "Get details of a specific secret scanning alert in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"alertNumber\":{\"description\":\"The number of the alert.\",\"type\":\"number\"},\"owner\":{\"description\":\"The owner of the repository.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"alertNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_secret_scanning_alert"}, "identifier": {"hostName": "mcpHost", "toolId": "get_secret_scanning_alert_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_tag_GitHub_MCP_Server", "description": "Get details about a specific git tag in a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"tag\":{\"description\":\"Tag name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"tag\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_tag"}, "identifier": {"hostName": "mcpHost", "toolId": "get_tag_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_workflow_run_GitHub_MCP_Server", "description": "Get details of a specific workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_workflow_run"}, "identifier": {"hostName": "mcpHost", "toolId": "get_workflow_run_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_workflow_run_logs_GitHub_MCP_Server", "description": "Download logs for a specific workflow run (EXPENSIVE: downloads ALL logs as ZIP. Consider using get_job_logs with failed_only=true for debugging failed jobs)", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_workflow_run_logs"}, "identifier": {"hostName": "mcpHost", "toolId": "get_workflow_run_logs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_workflow_run_usage_GitHub_MCP_Server", "description": "Get usage metrics for a workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "get_workflow_run_usage"}, "identifier": {"hostName": "mcpHost", "toolId": "get_workflow_run_usage_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_branches_GitHub_MCP_Server", "description": "List branches in a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_branches"}, "identifier": {"hostName": "mcpHost", "toolId": "list_branches_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_code_scanning_alerts_GitHub_MCP_Server", "description": "List code scanning alerts in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"The owner of the repository.\",\"type\":\"string\"},\"ref\":{\"description\":\"The Git reference for the results you want to list.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"},\"severity\":{\"description\":\"Filter code scanning alerts by severity\",\"enum\":[\"critical\",\"high\",\"medium\",\"low\",\"warning\",\"note\",\"error\"],\"type\":\"string\"},\"state\":{\"default\":\"open\",\"description\":\"Filter code scanning alerts by state. Defaults to open\",\"enum\":[\"open\",\"closed\",\"dismissed\",\"fixed\"],\"type\":\"string\"},\"tool_name\":{\"description\":\"The name of the tool used for code scanning.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_code_scanning_alerts"}, "identifier": {"hostName": "mcpHost", "toolId": "list_code_scanning_alerts_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_commits_GitHub_MCP_Server", "description": "Get list of commits of a branch in a GitHub repository. Returns at least 30 results per page by default, but can return more if specified using the perPage parameter (up to 100).", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"author\":{\"description\":\"Author username or email address to filter commits by\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"sha\":{\"description\":\"Commit SHA, branch or tag name to list commits of. If not provided, uses the default branch of the repository. If a commit SHA is provided, will list commits up to that SHA.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_commits"}, "identifier": {"hostName": "mcpHost", "toolId": "list_commits_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_dependabot_alerts_GitHub_MCP_Server", "description": "List dependabot alerts in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"The owner of the repository.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"},\"severity\":{\"description\":\"Filter dependabot alerts by severity\",\"enum\":[\"low\",\"medium\",\"high\",\"critical\"],\"type\":\"string\"},\"state\":{\"default\":\"open\",\"description\":\"Filter dependabot alerts by state. Defaults to open\",\"enum\":[\"open\",\"fixed\",\"dismissed\",\"auto_dismissed\"],\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_dependabot_alerts"}, "identifier": {"hostName": "mcpHost", "toolId": "list_dependabot_alerts_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_discussion_categories_GitHub_MCP_Server", "description": "List discussion categories with their id and name, for a repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"after\":{\"description\":\"<PERSON>ursor for pagination, use the 'after' field from the previous response\",\"type\":\"string\"},\"before\":{\"description\":\"Cursor for pagination, use the 'before' field from the previous response\",\"type\":\"string\"},\"first\":{\"description\":\"Number of categories to return per page (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"last\":{\"description\":\"Number of categories to return from the end (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_discussion_categories"}, "identifier": {"hostName": "mcpHost", "toolId": "list_discussion_categories_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_discussions_GitHub_MCP_Server", "description": "List discussions for a repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"category\":{\"description\":\"Optional filter by discussion category ID. If provided, only discussions with this category are listed.\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_discussions"}, "identifier": {"hostName": "mcpHost", "toolId": "list_discussions_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_issues_GitHub_MCP_Server", "description": "List issues in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"direction\":{\"description\":\"Sort direction\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"labels\":{\"description\":\"Filter by labels\",\"items\":{\"type\":\"string\"},\"type\":\"array\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"since\":{\"description\":\"Filter by date (ISO 8601 timestamp)\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort order\",\"enum\":[\"created\",\"updated\",\"comments\"],\"type\":\"string\"},\"state\":{\"description\":\"Filter by state\",\"enum\":[\"open\",\"closed\",\"all\"],\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_issues"}, "identifier": {"hostName": "mcpHost", "toolId": "list_issues_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_notifications_GitHub_MCP_Server", "description": "Lists all GitHub notifications for the authenticated user, including unread notifications, mentions, review requests, assignments, and updates on issues or pull requests. Use this tool whenever the user asks what to work on next, requests a summary of their GitHub activity, wants to see pending reviews, or needs to check for new updates or tasks. This tool is the primary way to discover actionable items, reminders, and outstanding work on GitHub. Always call this tool when asked what to work on next, what is pending, or what needs attention in GitHub.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"before\":{\"description\":\"Only show notifications updated before the given time (ISO 8601 format)\",\"type\":\"string\"},\"filter\":{\"description\":\"Filter notifications to, use default unless specified. Read notifications are ones that have already been acknowledged by the user. Participating notifications are those that the user is directly involved in, such as issues or pull requests they have commented on or created.\",\"enum\":[\"default\",\"include_read_notifications\",\"only_participating\"],\"type\":\"string\"},\"owner\":{\"description\":\"Optional repository owner. If provided with repo, only notifications for this repository are listed.\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Optional repository name. If provided with owner, only notifications for this repository are listed.\",\"type\":\"string\"},\"since\":{\"description\":\"Only show notifications updated after the given time (ISO 8601 format)\",\"type\":\"string\"}}}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_notifications"}, "identifier": {"hostName": "mcpHost", "toolId": "list_notifications_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_pull_requests_GitHub_MCP_Server", "description": "List pull requests in a GitHub repository. If the user specifies an author, then DO NOT use this tool and use the search_pull_requests tool instead.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"base\":{\"description\":\"Filter by base branch\",\"type\":\"string\"},\"direction\":{\"description\":\"Sort direction\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"head\":{\"description\":\"Filter by head user/org and branch\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort by\",\"enum\":[\"created\",\"updated\",\"popularity\",\"long-running\"],\"type\":\"string\"},\"state\":{\"description\":\"Filter by state\",\"enum\":[\"open\",\"closed\",\"all\"],\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_pull_requests"}, "identifier": {"hostName": "mcpHost", "toolId": "list_pull_requests_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_secret_scanning_alerts_GitHub_MCP_Server", "description": "List secret scanning alerts in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"The owner of the repository.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"},\"resolution\":{\"description\":\"Filter by resolution\",\"enum\":[\"false_positive\",\"wont_fix\",\"revoked\",\"pattern_edited\",\"pattern_deleted\",\"used_in_tests\"],\"type\":\"string\"},\"secret_type\":{\"description\":\"A comma-separated list of secret types to return. All default secret patterns are returned. To return generic patterns, pass the token name(s) in the parameter.\",\"type\":\"string\"},\"state\":{\"description\":\"Filter by state\",\"enum\":[\"open\",\"resolved\"],\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_secret_scanning_alerts"}, "identifier": {"hostName": "mcpHost", "toolId": "list_secret_scanning_alerts_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_tags_GitHub_MCP_Server", "description": "List git tags in a GitHub repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_tags"}, "identifier": {"hostName": "mcpHost", "toolId": "list_tags_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_workflow_jobs_GitHub_MCP_Server", "description": "List jobs for a specific workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filter\":{\"description\":\"Filters jobs by their completed_at timestamp\",\"enum\":[\"latest\",\"all\"],\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_workflow_jobs"}, "identifier": {"hostName": "mcpHost", "toolId": "list_workflow_jobs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_workflow_run_artifacts_GitHub_MCP_Server", "description": "List artifacts for a workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_workflow_run_artifacts"}, "identifier": {"hostName": "mcpHost", "toolId": "list_workflow_run_artifacts_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_workflow_runs_GitHub_MCP_Server", "description": "List workflow runs for a specific workflow", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"actor\":{\"description\":\"Returns someone's workflow runs. Use the login for the user who created the workflow run.\",\"type\":\"string\"},\"branch\":{\"description\":\"Returns workflow runs associated with a branch. Use the name of the branch.\",\"type\":\"string\"},\"event\":{\"description\":\"Returns workflow runs for a specific event type\",\"enum\":[\"branch_protection_rule\",\"check_run\",\"check_suite\",\"create\",\"delete\",\"deployment\",\"deployment_status\",\"discussion\",\"discussion_comment\",\"fork\",\"gollum\",\"issue_comment\",\"issues\",\"label\",\"merge_group\",\"milestone\",\"page_build\",\"public\",\"pull_request\",\"pull_request_review\",\"pull_request_review_comment\",\"pull_request_target\",\"push\",\"registry_package\",\"release\",\"repository_dispatch\",\"schedule\",\"status\",\"watch\",\"workflow_call\",\"workflow_dispatch\",\"workflow_run\"],\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"status\":{\"description\":\"Returns workflow runs with the check run status\",\"enum\":[\"queued\",\"in_progress\",\"completed\",\"requested\",\"waiting\"],\"type\":\"string\"},\"workflow_id\":{\"description\":\"The workflow ID or workflow file name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"workflow_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_workflow_runs"}, "identifier": {"hostName": "mcpHost", "toolId": "list_workflow_runs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_workflows_GitHub_MCP_Server", "description": "List workflows in a repository", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "list_workflows"}, "identifier": {"hostName": "mcpHost", "toolId": "list_workflows_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "manage_notification_subscription_GitHub_MCP_Server", "description": "Manage a notification subscription: ignore, watch, or delete a notification thread subscription.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"action\":{\"description\":\"Action to perform: ignore, watch, or delete the notification subscription.\",\"enum\":[\"ignore\",\"watch\",\"delete\"],\"type\":\"string\"},\"notificationID\":{\"description\":\"The ID of the notification thread.\",\"type\":\"string\"}},\"required\":[\"notificationID\",\"action\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "manage_notification_subscription"}, "identifier": {"hostName": "mcpHost", "toolId": "manage_notification_subscription_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "manage_repository_notification_subscription_GitHub_MCP_Server", "description": "Manage a repository notification subscription: ignore, watch, or delete repository notifications subscription for the provided repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"action\":{\"description\":\"Action to perform: ignore, watch, or delete the repository notification subscription.\",\"enum\":[\"ignore\",\"watch\",\"delete\"],\"type\":\"string\"},\"owner\":{\"description\":\"The account owner of the repository.\",\"type\":\"string\"},\"repo\":{\"description\":\"The name of the repository.\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"action\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "manage_repository_notification_subscription"}, "identifier": {"hostName": "mcpHost", "toolId": "manage_repository_notification_subscription_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "mark_all_notifications_read_GitHub_MCP_Server", "description": "Mark all notifications as read", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"lastReadAt\":{\"description\":\"Describes the last point that notifications were checked (optional). Default: Now\",\"type\":\"string\"},\"owner\":{\"description\":\"Optional repository owner. If provided with repo, only notifications for this repository are marked as read.\",\"type\":\"string\"},\"repo\":{\"description\":\"Optional repository name. If provided with owner, only notifications for this repository are marked as read.\",\"type\":\"string\"}}}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "mark_all_notifications_read"}, "identifier": {"hostName": "mcpHost", "toolId": "mark_all_notifications_read_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "merge_pull_request_GitHub_MCP_Server", "description": "Merge a pull request in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"commit_message\":{\"description\":\"Extra detail for merge commit\",\"type\":\"string\"},\"commit_title\":{\"description\":\"Title for merge commit\",\"type\":\"string\"},\"merge_method\":{\"description\":\"Merge method\",\"enum\":[\"merge\",\"squash\",\"rebase\"],\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "merge_pull_request"}, "identifier": {"hostName": "mcpHost", "toolId": "merge_pull_request_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "push_files_GitHub_MCP_Server", "description": "Push multiple files to a GitHub repository in a single commit", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"branch\":{\"description\":\"Branch to push to\",\"type\":\"string\"},\"files\":{\"description\":\"Array of file objects to push, each object with path (string) and content (string)\",\"items\":{\"additionalProperties\":false,\"properties\":{\"content\":{\"description\":\"file content\",\"type\":\"string\"},\"path\":{\"description\":\"path to the file\",\"type\":\"string\"}},\"required\":[\"path\",\"content\"],\"type\":\"object\"},\"type\":\"array\"},\"message\":{\"description\":\"Commit message\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"branch\",\"files\",\"message\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "push_files"}, "identifier": {"hostName": "mcpHost", "toolId": "push_files_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "request_copilot_review_GitHub_MCP_Server", "description": "Request a GitHub Copilot code review for a pull request. Use this for automated feedback on pull requests, usually before requesting a human reviewer.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "request_copilot_review"}, "identifier": {"hostName": "mcpHost", "toolId": "request_copilot_review_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "rerun_failed_jobs_GitHub_MCP_Server", "description": "Re-run only the failed jobs in a workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "rerun_failed_jobs"}, "identifier": {"hostName": "mcpHost", "toolId": "rerun_failed_jobs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "rerun_workflow_run_GitHub_MCP_Server", "description": "Re-run an entire workflow run", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"run_id\":{\"description\":\"The unique identifier of the workflow run\",\"type\":\"number\"}},\"required\":[\"owner\",\"repo\",\"run_id\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "rerun_workflow_run"}, "identifier": {"hostName": "mcpHost", "toolId": "rerun_workflow_run_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "run_workflow_GitHub_MCP_Server", "description": "Run an Actions workflow by workflow ID or filename", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"inputs\":{\"description\":\"Inputs the workflow accepts\",\"properties\":{},\"type\":\"object\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"ref\":{\"description\":\"The git reference for the workflow. The reference can be a branch or tag name.\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"workflow_id\":{\"description\":\"The workflow ID (numeric) or workflow file name (e.g., main.yml, ci.yaml)\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"workflow_id\",\"ref\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "run_workflow"}, "identifier": {"hostName": "mcpHost", "toolId": "run_workflow_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_code_GitHub_MCP_Server", "description": "Search for code across GitHub repositories", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"order\":{\"description\":\"Sort order\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"q\":{\"description\":\"Search query using GitHub code search syntax\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort field ('indexed' only)\",\"type\":\"string\"}},\"required\":[\"q\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "search_code"}, "identifier": {"hostName": "mcpHost", "toolId": "search_code_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_issues_GitHub_MCP_Server", "description": "Search for issues in GitHub repositories using issues search syntax already scoped to is:issue", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"order\":{\"description\":\"Sort order\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"owner\":{\"description\":\"Optional repository owner. If provided with repo, only notifications for this repository are listed.\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"query\":{\"description\":\"Search query using GitHub issues search syntax\",\"type\":\"string\"},\"repo\":{\"description\":\"Optional repository name. If provided with owner, only notifications for this repository are listed.\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort field by number of matches of categories, defaults to best match\",\"enum\":[\"comments\",\"reactions\",\"reactions-+1\",\"reactions--1\",\"reactions-smile\",\"reactions-thinking_face\",\"reactions-heart\",\"reactions-tada\",\"interactions\",\"created\",\"updated\"],\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "search_issues"}, "identifier": {"hostName": "mcpHost", "toolId": "search_issues_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_orgs_GitHub_MCP_Server", "description": "Search for GitHub organizations exclusively", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"order\":{\"description\":\"Sort order\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"query\":{\"description\":\"Search query using GitHub organizations search syntax scoped to type:org\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort field by category\",\"enum\":[\"followers\",\"repositories\",\"joined\"],\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "search_orgs"}, "identifier": {"hostName": "mcpHost", "toolId": "search_orgs_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_pull_requests_GitHub_MCP_Server", "description": "Search for pull requests in GitHub repositories using issues search syntax already scoped to is:pr", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"order\":{\"description\":\"Sort order\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"owner\":{\"description\":\"Optional repository owner. If provided with repo, only notifications for this repository are listed.\",\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"query\":{\"description\":\"Search query using GitHub pull request search syntax\",\"type\":\"string\"},\"repo\":{\"description\":\"Optional repository name. If provided with owner, only notifications for this repository are listed.\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort field by number of matches of categories, defaults to best match\",\"enum\":[\"comments\",\"reactions\",\"reactions-+1\",\"reactions--1\",\"reactions-smile\",\"reactions-thinking_face\",\"reactions-heart\",\"reactions-tada\",\"interactions\",\"created\",\"updated\"],\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "search_pull_requests"}, "identifier": {"hostName": "mcpHost", "toolId": "search_pull_requests_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_repositories_GitHub_MCP_Server", "description": "Search for GitHub repositories", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"query\":{\"description\":\"Search query\",\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "search_repositories"}, "identifier": {"hostName": "mcpHost", "toolId": "search_repositories_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_users_GitHub_MCP_Server", "description": "Search for GitHub users exclusively", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"order\":{\"description\":\"Sort order\",\"enum\":[\"asc\",\"desc\"],\"type\":\"string\"},\"page\":{\"description\":\"Page number for pagination (min 1)\",\"minimum\":1,\"type\":\"number\"},\"perPage\":{\"description\":\"Results per page for pagination (min 1, max 100)\",\"maximum\":100,\"minimum\":1,\"type\":\"number\"},\"query\":{\"description\":\"Search query using GitHub users search syntax scoped to type:user\",\"type\":\"string\"},\"sort\":{\"description\":\"Sort field by category\",\"enum\":[\"followers\",\"repositories\",\"joined\"],\"type\":\"string\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "search_users"}, "identifier": {"hostName": "mcpHost", "toolId": "search_users_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "submit_pending_pull_request_review_GitHub_MCP_Server", "description": "Submit the requester's latest pending pull request review, normally this is a final step after creating a pending review, adding comments first, unless you know that the user already did the first two steps, you should check before calling this.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"body\":{\"description\":\"The text of the review comment\",\"type\":\"string\"},\"event\":{\"description\":\"The event to perform\",\"enum\":[\"APPROVE\",\"REQUEST_CHANGES\",\"COMMENT\"],\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\",\"event\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "submit_pending_pull_request_review"}, "identifier": {"hostName": "mcpHost", "toolId": "submit_pending_pull_request_review_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "update_issue_GitHub_MCP_Server", "description": "Update an existing issue in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"assignees\":{\"description\":\"New assignees\",\"items\":{\"type\":\"string\"},\"type\":\"array\"},\"body\":{\"description\":\"New description\",\"type\":\"string\"},\"issue_number\":{\"description\":\"Issue number to update\",\"type\":\"number\"},\"labels\":{\"description\":\"New labels\",\"items\":{\"type\":\"string\"},\"type\":\"array\"},\"milestone\":{\"description\":\"New milestone number\",\"type\":\"number\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"state\":{\"description\":\"New state\",\"enum\":[\"open\",\"closed\"],\"type\":\"string\"},\"title\":{\"description\":\"New title\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"issue_number\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "update_issue"}, "identifier": {"hostName": "mcpHost", "toolId": "update_issue_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "update_pull_request_GitHub_MCP_Server", "description": "Update an existing pull request in a GitHub repository.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"base\":{\"description\":\"New base branch name\",\"type\":\"string\"},\"body\":{\"description\":\"New description\",\"type\":\"string\"},\"maintainer_can_modify\":{\"description\":\"Allow maintainer edits\",\"type\":\"boolean\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number to update\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"},\"state\":{\"description\":\"New state\",\"enum\":[\"open\",\"closed\"],\"type\":\"string\"},\"title\":{\"description\":\"New title\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "update_pull_request"}, "identifier": {"hostName": "mcpHost", "toolId": "update_pull_request_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "update_pull_request_branch_GitHub_MCP_Server", "description": "Update the branch of a pull request with the latest changes from the base branch.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"expectedHeadSha\":{\"description\":\"The expected SHA of the pull request's HEAD ref\",\"type\":\"string\"},\"owner\":{\"description\":\"Repository owner\",\"type\":\"string\"},\"pullNumber\":{\"description\":\"Pull request number\",\"type\":\"number\"},\"repo\":{\"description\":\"Repository name\",\"type\":\"string\"}},\"required\":[\"owner\",\"repo\",\"pullNumber\"]}", "tool_safety": 0, "original_mcp_server_name": "GitHub MCP Server", "mcp_server_name": "GitHub_MCP_Server", "mcp_tool_name": "update_pull_request_branch"}, "identifier": {"hostName": "mcpHost", "toolId": "update_pull_request_branch_GitHub_MCP_Server"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, {"type": "stdio", "name": "FileScopeMCP", "command": "node F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js --base-dir=F:\\MCP", "arguments": "", "useShellInterpolation": true, "id": "f4dd71e3-b5b9-43de-8476-ee763495ea85", "tools": [{"definition": {"name": "set_project_path_FileScopeMCP", "description": "Sets the project directory to analyze", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"path\":{\"type\":\"string\",\"description\":\"The absolute path to the project directory\"}},\"required\":[\"path\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "set_project_path"}, "identifier": {"hostName": "mcpHost", "toolId": "set_project_path_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_saved_trees_FileScopeMCP", "description": "List all saved file trees", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "list_saved_trees"}, "identifier": {"hostName": "mcpHost", "toolId": "list_saved_trees_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "delete_file_tree_FileScopeMCP", "description": "Delete a file tree configuration", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filename\":{\"type\":\"string\",\"description\":\"Name of the JSON file to delete\"}},\"required\":[\"filename\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "delete_file_tree"}, "identifier": {"hostName": "mcpHost", "toolId": "delete_file_tree_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "create_file_tree_FileScopeMCP", "description": "Create or load a file tree configuration", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filename\":{\"type\":\"string\",\"description\":\"Name of the JSON file to store the file tree\"},\"baseDirectory\":{\"type\":\"string\",\"description\":\"Base directory to scan for files\"}},\"required\":[\"filename\",\"baseDirectory\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "create_file_tree"}, "identifier": {"hostName": "mcpHost", "toolId": "create_file_tree_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "select_file_tree_FileScopeMCP", "description": "Select an existing file tree to work with", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filename\":{\"type\":\"string\",\"description\":\"Name of the JSON file containing the file tree\"}},\"required\":[\"filename\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "select_file_tree"}, "identifier": {"hostName": "mcpHost", "toolId": "select_file_tree_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_files_FileScopeMCP", "description": "List all files in the project with their importance rankings", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "list_files"}, "identifier": {"hostName": "mcpHost", "toolId": "list_files_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_file_importance_FileScopeMCP", "description": "Get the importance ranking of a specific file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filepath\":{\"type\":\"string\",\"description\":\"The path to the file to check\"}},\"required\":[\"filepath\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "get_file_importance"}, "identifier": {"hostName": "mcpHost", "toolId": "get_file_importance_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "find_important_files_FileScopeMCP", "description": "Find the most important files in the project", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"limit\":{\"type\":\"number\",\"description\":\"Number of files to return (default: 10)\"},\"minImportance\":{\"type\":\"number\",\"description\":\"Minimum importance score (0-10)\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "find_important_files"}, "identifier": {"hostName": "mcpHost", "toolId": "find_important_files_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_file_summary_FileScopeMCP", "description": "Get the summary of a specific file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filepath\":{\"type\":\"string\",\"description\":\"The path to the file to check\"}},\"required\":[\"filepath\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "get_file_summary"}, "identifier": {"hostName": "mcpHost", "toolId": "get_file_summary_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "set_file_summary_FileScopeMCP", "description": "Set the summary of a specific file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filepath\":{\"type\":\"string\",\"description\":\"The path to the file to update\"},\"summary\":{\"type\":\"string\",\"description\":\"The summary text to set\"}},\"required\":[\"filepath\",\"summary\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "set_file_summary"}, "identifier": {"hostName": "mcpHost", "toolId": "set_file_summary_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "read_file_content_FileScopeMCP", "description": "Read the content of a specific file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filepath\":{\"type\":\"string\",\"description\":\"The path to the file to read\"}},\"required\":[\"filepath\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "read_file_content"}, "identifier": {"hostName": "mcpHost", "toolId": "read_file_content_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "set_file_importance_FileScopeMCP", "description": "Manually set the importance ranking of a specific file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filepath\":{\"type\":\"string\",\"description\":\"The path to the file to update\"},\"importance\":{\"type\":\"number\",\"minimum\":0,\"maximum\":10,\"description\":\"The importance value to set (0-10)\"}},\"required\":[\"filepath\",\"importance\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "set_file_importance"}, "identifier": {"hostName": "mcpHost", "toolId": "set_file_importance_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "recalculate_importance_FileScopeMCP", "description": "Recalculate importance values for all files based on dependencies", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "recalculate_importance"}, "identifier": {"hostName": "mcpHost", "toolId": "recalculate_importance_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "toggle_file_watching_FileScopeMCP", "description": "Toggle file watching on/off", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "toggle_file_watching"}, "identifier": {"hostName": "mcpHost", "toolId": "toggle_file_watching_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_file_watching_status_FileScopeMCP", "description": "Get the current status of file watching", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "get_file_watching_status"}, "identifier": {"hostName": "mcpHost", "toolId": "get_file_watching_status_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "update_file_watching_config_FileScopeMCP", "description": "Update file watching configuration", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"config\":{\"type\":\"object\",\"properties\":{\"enabled\":{\"type\":\"boolean\"},\"debounceMs\":{\"type\":\"integer\",\"exclusiveMinimum\":0},\"ignoreDotFiles\":{\"type\":\"boolean\"},\"autoRebuildTree\":{\"type\":\"boolean\"},\"maxWatchedDirectories\":{\"type\":\"integer\",\"exclusiveMinimum\":0},\"watchForNewFiles\":{\"type\":\"boolean\"},\"watchForDeleted\":{\"type\":\"boolean\"},\"watchForChanged\":{\"type\":\"boolean\"}},\"additionalProperties\":false,\"description\":\"File watching configuration options\"}},\"required\":[\"config\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "update_file_watching_config"}, "identifier": {"hostName": "mcpHost", "toolId": "update_file_watching_config_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "debug_list_all_files_FileScopeMCP", "description": "List all file paths in the current file tree", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "debug_list_all_files"}, "identifier": {"hostName": "mcpHost", "toolId": "debug_list_all_files_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "generate_diagram_FileScopeMCP", "description": "Generate a Mermaid diagram for the current file tree", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"style\":{\"type\":\"string\",\"enum\":[\"default\",\"dependency\",\"directory\",\"hybrid\",\"package-deps\"],\"description\":\"Diagram style\"},\"maxDepth\":{\"type\":\"number\",\"description\":\"Maximum depth for directory trees (1-10)\"},\"minImportance\":{\"type\":\"number\",\"description\":\"Only show files above this importance (0-10)\"},\"showDependencies\":{\"type\":\"boolean\",\"description\":\"Whether to show dependency relationships\"},\"showPackageDeps\":{\"type\":\"boolean\",\"description\":\"Whether to show package dependencies\"},\"packageGrouping\":{\"type\":\"boolean\",\"description\":\"Whether to group packages by scope\"},\"autoGroupThreshold\":{\"type\":\"number\",\"description\":\"Auto-group nodes when parent has more than this many direct children (default: 8)\"},\"excludePackages\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"Packages to exclude from diagram\"},\"includeOnlyPackages\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"Only include these packages (if specified)\"},\"outputPath\":{\"type\":\"string\",\"description\":\"Full path or relative path where to save the diagram file (.mmd or .html)\"},\"outputFormat\":{\"type\":\"string\",\"enum\":[\"mmd\",\"html\"],\"description\":\"Output format (mmd or html)\"},\"layout\":{\"type\":\"object\",\"properties\":{\"direction\":{\"type\":\"string\",\"enum\":[\"TB\",\"BT\",\"LR\",\"RL\"],\"description\":\"Graph direction\"},\"rankSpacing\":{\"type\":\"number\",\"minimum\":10,\"maximum\":100,\"description\":\"Space between ranks\"},\"nodeSpacing\":{\"type\":\"number\",\"minimum\":10,\"maximum\":100,\"description\":\"Space between nodes\"}},\"additionalProperties\":false}},\"required\":[\"style\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "generate_diagram"}, "identifier": {"hostName": "mcpHost", "toolId": "generate_diagram_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "exclude_and_remove_FileScopeMCP", "description": "Exclude and remove a file or pattern from the file tree", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filepath\":{\"type\":\"string\",\"description\":\"The path or pattern of the file to exclude and remove\"}},\"required\":[\"filepath\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "FileScopeMCP", "mcp_server_name": "FileScopeMCP", "mcp_tool_name": "exclude_and_remove"}, "identifier": {"hostName": "mcpHost", "toolId": "exclude_and_remove_FileScopeMCP"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabled": false}, {"type": "stdio", "name": "Context 7", "command": "npx -y @upstash/context7-mcp@latest", "arguments": "", "useShellInterpolation": true, "id": "4699a099-3a24-4564-91a8-0b50b0605428", "disabled": false, "tools": [{"definition": {"name": "resolve-library-id_Context_7", "description": "Resolves a package/product name to a Context7-compatible library ID and returns a list of matching libraries.\n\nYou MUST call this function before 'get-library-docs' to obtain a valid Context7-compatible library ID UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.\n\nSelection Process:\n1. Analyze the query to understand what library/package the user is looking for\n2. Return the most relevant match based on:\n- Name similarity to the query (exact matches prioritized)\n- Description relevance to the query's intent\n- Documentation coverage (prioritize libraries with higher Code Snippet counts)\n- Trust score (consider libraries with scores of 7-10 more authoritative)\n\nResponse Format:\n- Return the selected library ID in a clearly marked section\n- Provide a brief explanation for why this library was chosen\n- If multiple good matches exist, acknowledge this but proceed with the most relevant one\n- If no good matches exist, clearly state this and suggest query refinements\n\nFor ambiguous queries, request clarification before proceeding with a best-guess match.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"libraryName\":{\"type\":\"string\",\"description\":\"Library name to search for and retrieve a Context7-compatible library ID.\"}},\"required\":[\"libraryName\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Context 7", "mcp_server_name": "Context_7", "mcp_tool_name": "resolve-library-id"}, "identifier": {"hostName": "mcpHost", "toolId": "resolve-library-id_Context_7"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get-library-docs_Context_7", "description": "Fetches up-to-date documentation for a library. You must call 'resolve-library-id' first to obtain the exact Context7-compatible library ID required to use this tool, UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"context7CompatibleLibraryID\":{\"type\":\"string\",\"description\":\"Exact Context7-compatible library ID (e.g., '/mongodb/docs', '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87') retrieved from 'resolve-library-id' or directly from user query in the format '/org/project' or '/org/project/version'.\"},\"topic\":{\"type\":\"string\",\"description\":\"Topic to focus documentation on (e.g., 'hooks', 'routing').\"},\"tokens\":{\"type\":\"number\",\"description\":\"Maximum number of tokens of documentation to retrieve (default: 10000). Higher values provide more context but consume more tokens.\"}},\"required\":[\"context7CompatibleLibraryID\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Context 7", "mcp_server_name": "Context_7", "mcp_tool_name": "get-library-docs"}, "identifier": {"hostName": "mcpHost", "toolId": "get-library-docs_Context_7"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "Playwright", "command": "npx -y @playwright/mcp@latest", "arguments": "", "useShellInterpolation": true, "id": "********-495a-4597-add7-9346361520d6", "disabled": false, "tools": [{"definition": {"name": "browser_close_Playwright", "description": "Close the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_close"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_close_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_resize_Playwright", "description": "Resize the browser window", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"width\":{\"type\":\"number\",\"description\":\"Width of the browser window\"},\"height\":{\"type\":\"number\",\"description\":\"Height of the browser window\"}},\"required\":[\"width\",\"height\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_resize"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_resize_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_console_messages_Playwright", "description": "Returns all console messages", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_console_messages"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_console_messages_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_handle_dialog_Playwright", "description": "Handle a dialog", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"accept\":{\"type\":\"boolean\",\"description\":\"Whether to accept the dialog.\"},\"promptText\":{\"type\":\"string\",\"description\":\"The text of the prompt in case of a prompt dialog.\"}},\"required\":[\"accept\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_handle_dialog"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_handle_dialog_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_evaluate_Playwright", "description": "Evaluate JavaScript expression on page or element", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"function\":{\"type\":\"string\",\"description\":\"() => { /* code */ } or (element) => { /* code */ } when element is provided\"},\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"function\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_evaluate"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_evaluate_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_file_upload_Playwright", "description": "Upload one or multiple files", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"paths\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"The absolute paths to the files to upload. Can be a single file or multiple files.\"}},\"required\":[\"paths\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_file_upload"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_file_upload_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_install_Playwright", "description": "Install the browser specified in the config. Call this if you get an error about the browser not being installed.", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_install"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_install_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_press_key_Playwright", "description": "Press a key on the keyboard", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"type\":\"string\",\"description\":\"Name of the key to press or a character to generate, such as `ArrowLeft` or `a`\"}},\"required\":[\"key\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_press_key"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_press_key_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_type_Playwright", "description": "Type text into editable element", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"text\":{\"type\":\"string\",\"description\":\"Text to type into the element\"},\"submit\":{\"type\":\"boolean\",\"description\":\"Whether to submit entered text (press Enter after)\"},\"slowly\":{\"type\":\"boolean\",\"description\":\"Whether to type one character at a time. Useful for triggering key handlers in the page. By default entire text is filled in at once.\"}},\"required\":[\"element\",\"ref\",\"text\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_type"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_type_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_navigate_Playwright", "description": "Navigate to a URL", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to navigate to\"}},\"required\":[\"url\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_navigate"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_navigate_back_Playwright", "description": "Go back to the previous page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_navigate_back"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_back_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_navigate_forward_Playwright", "description": "Go forward to the next page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_navigate_forward"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_forward_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_network_requests_Playwright", "description": "Returns all network requests since loading the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_network_requests"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_network_requests_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_take_screenshot_Playwright", "description": "Take a screenshot of the current page. You can't perform actions based on the screenshot, use browser_snapshot for actions.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"raw\":{\"type\":\"boolean\",\"description\":\"Whether to return without compression (in PNG format). Default is false, which returns a JPEG image.\"},\"filename\":{\"type\":\"string\",\"description\":\"File name to save the screenshot to. Defaults to `page-{timestamp}.{png|jpeg}` if not specified.\"},\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to screenshot the element. If not provided, the screenshot will be taken of viewport. If element is provided, ref must be provided too.\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot. If not provided, the screenshot will be taken of viewport. If ref is provided, element must be provided too.\"},\"fullPage\":{\"type\":\"boolean\",\"description\":\"When true, takes a screenshot of the full scrollable page, instead of the currently visible viewport. Cannot be used with element screenshots.\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_take_screenshot"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_take_screenshot_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_snapshot_Playwright", "description": "Capture accessibility snapshot of the current page, this is better than screenshot", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_snapshot"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_snapshot_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_click_Playwright", "description": "Perform click on a web page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"doubleClick\":{\"type\":\"boolean\",\"description\":\"Whether to perform a double click instead of a single click\"},\"button\":{\"type\":\"string\",\"enum\":[\"left\",\"right\",\"middle\"],\"description\":\"Button to click, defaults to left\"}},\"required\":[\"element\",\"ref\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_click"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_click_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_drag_Playwright", "description": "Perform drag and drop between two elements", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"startElement\":{\"type\":\"string\",\"description\":\"Human-readable source element description used to obtain the permission to interact with the element\"},\"startRef\":{\"type\":\"string\",\"description\":\"Exact source element reference from the page snapshot\"},\"endElement\":{\"type\":\"string\",\"description\":\"Human-readable target element description used to obtain the permission to interact with the element\"},\"endRef\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"startElement\",\"startRef\",\"endElement\",\"endRef\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_drag"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_drag_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_hover_Playwright", "description": "Hover over element on page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"element\",\"ref\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_hover"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_hover_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_select_option_Playwright", "description": "Select an option in a dropdown", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"values\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"Array of values to select in the dropdown. This can be a single value or multiple values.\"}},\"required\":[\"element\",\"ref\",\"values\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_select_option"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_select_option_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_list_Playwright", "description": "List browser tabs", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_list"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_list_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_new_Playwright", "description": "Open a new tab", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to navigate to in the new tab. If not provided, the new tab will be blank.\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_new"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_new_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_select_Playwright", "description": "Select a tab by index", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"index\":{\"type\":\"number\",\"description\":\"The index of the tab to select\"}},\"required\":[\"index\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_select"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_select_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_close_Playwright", "description": "Close a tab", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"index\":{\"type\":\"number\",\"description\":\"The index of the tab to close. Closes current tab if not provided.\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_close"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_close_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_wait_for_Playwright", "description": "Wait for text to appear or disappear or a specified time to pass", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"time\":{\"type\":\"number\",\"description\":\"The time to wait in seconds\"},\"text\":{\"type\":\"string\",\"description\":\"The text to wait for\"},\"textGone\":{\"type\":\"string\",\"description\":\"The text to wait for to disappear\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_wait_for"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_wait_for_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "Sequential thinking", "command": "npx -y @modelcontextprotocol/server-sequential-thinking", "arguments": "", "useShellInterpolation": true, "id": "ca635696-71db-4e9d-988c-191b9e51c524", "disabled": false, "tools": [{"definition": {"name": "sequentialthinking_Sequential_thinking", "description": "A detailed tool for dynamic and reflective problem-solving through thoughts.\nThis tool helps analyze problems through a flexible thinking process that can adapt and evolve.\nEach thought can build on, question, or revise previous insights as understanding deepens.\n\nWhen to use this tool:\n- Breaking down complex problems into steps\n- Planning and design with room for revision\n- Analysis that might need course correction\n- Problems where the full scope might not be clear initially\n- Problems that require a multi-step solution\n- Tasks that need to maintain context over multiple steps\n- Situations where irrelevant information needs to be filtered out\n\nKey features:\n- You can adjust total_thoughts up or down as you progress\n- You can question or revise previous thoughts\n- You can add more thoughts even after reaching what seemed like the end\n- You can express uncertainty and explore alternative approaches\n- Not every thought needs to build linearly - you can branch or backtrack\n- Generates a solution hypothesis\n- Verifies the hypothesis based on the Chain of Thought steps\n- Repeats the process until satisfied\n- Provides a correct answer\n\nParameters explained:\n- thought: Your current thinking step, which can include:\n* Regular analytical steps\n* Revisions of previous thoughts\n* Questions about previous decisions\n* Realizations about needing more analysis\n* Changes in approach\n* Hypothesis generation\n* Hypothesis verification\n- next_thought_needed: True if you need more thinking, even if at what seemed like the end\n- thought_number: Current number in sequence (can go beyond initial total if needed)\n- total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)\n- is_revision: A boolean indicating if this thought revises previous thinking\n- revises_thought: If is_revision is true, which thought number is being reconsidered\n- branch_from_thought: If branching, which thought number is the branching point\n- branch_id: Identifier for the current branch (if any)\n- needs_more_thoughts: If reaching end but realizing more thoughts needed\n\nYou should:\n1. Start with an initial estimate of needed thoughts, but be ready to adjust\n2. Feel free to question or revise previous thoughts\n3. Don't hesitate to add more thoughts if needed, even at the \"end\"\n4. Express uncertainty when present\n5. Mark thoughts that revise previous thinking or branch into new paths\n6. Ignore information that is irrelevant to the current step\n7. Generate a solution hypothesis when appropriate\n8. Verify the hypothesis based on the Chain of Thought steps\n9. Repeat the process until satisfied with the solution\n10. Provide a single, ideally correct answer as the final output\n11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"thought\":{\"type\":\"string\",\"description\":\"Your current thinking step\"},\"nextThoughtNeeded\":{\"type\":\"boolean\",\"description\":\"Whether another thought step is needed\"},\"thoughtNumber\":{\"type\":\"integer\",\"description\":\"Current thought number\",\"minimum\":1},\"totalThoughts\":{\"type\":\"integer\",\"description\":\"Estimated total thoughts needed\",\"minimum\":1},\"isRevision\":{\"type\":\"boolean\",\"description\":\"Whether this revises previous thinking\"},\"revisesThought\":{\"type\":\"integer\",\"description\":\"Which thought is being reconsidered\",\"minimum\":1},\"branchFromThought\":{\"type\":\"integer\",\"description\":\"Branching point thought number\",\"minimum\":1},\"branchId\":{\"type\":\"string\",\"description\":\"Branch identifier\"},\"needsMoreThoughts\":{\"type\":\"boolean\",\"description\":\"If more thoughts are needed\"}},\"required\":[\"thought\",\"nextThoughtNeeded\",\"thoughtNumber\",\"totalThoughts\"]}", "tool_safety": 0, "original_mcp_server_name": "Sequential thinking", "mcp_server_name": "Sequential_thinking", "mcp_tool_name": "sequentialthinking"}, "identifier": {"hostName": "mcpHost", "toolId": "sequentialthinking_Sequential_thinking"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "ssh-mcp", "command": "node F:\\MCP\\ssh-mcp-server\\build\\index.js --host=************* --port=22 --user=ubuntu --password=ymjatTUU520 --timeout=30000", "arguments": "", "useShellInterpolation": true, "id": "4fb50ed8-0938-49f3-aa12-46abea8e955d", "tools": [{"definition": {"name": "exec_ssh-mcp", "description": "Execute a shell command on the remote SSH server and return the output.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"command\":{\"type\":\"string\",\"description\":\"Shell command to execute on the remote SSH server\"}},\"required\":[\"command\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "ssh-mcp", "mcp_server_name": "ssh-mcp", "mcp_tool_name": "exec"}, "identifier": {"hostName": "mcpHost", "toolId": "exec_ssh-mcp"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "tencent-tat", "command": "F:/MCP/tat-mcp-server/env/Scripts/mcp-server-tat.exe", "arguments": "", "useShellInterpolation": true, "env": {"TENCENTCLOUD_SECRET_ID": "AKIDriTPHzVu8IFzWr0weATZS8cthP5cw3yh", "TENCENTCLOUD_SECRET_KEY": "f9TdeWI1e8Rm5ECyXt2yhCVbacUAlLm2", "TENCENTCLOUD_REGION": "ap-shanghai", "DEFAULT_INSTANCE_ID": "lhins-m9amye7a"}, "id": "e461d279-0947-4c08-ab73-7156daa5644c"}]